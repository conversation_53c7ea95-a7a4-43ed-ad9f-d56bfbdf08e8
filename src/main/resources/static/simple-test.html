<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>简单测试</title>
</head>
<body>
<div id="app">
    <h1>{{ message }}</h1>
    <p>当前时间: {{ currentTime }}</p>
    <div v-for="task in tasks" :key="task.id">
        <strong>{{ task.title }}</strong> - 创建: {{ formatTime(task.createdDate) }}
    </div>
</div>

<script src="js/libs/vue.min.js"></script>
<script>
console.log('Vue loaded:', typeof Vue);

new Vue({
    el: '#app',
    data: {
        message: 'Hello Vue!',
        currentTime: new Date().toLocaleString(),
        tasks: [
            {
                id: 1,
                title: '测试任务',
                createdDate: '2025-05-21 09:50:10'
            }
        ]
    },
    methods: {
        formatTime(dateStr) {
            if (!dateStr) return '';
            
            const date = new Date(dateStr);
            const now = new Date();
            const diffMs = now - date;
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
            
            if (diffDays < 7) {
                return `${diffDays}天前`;
            } else {
                return date.toLocaleDateString('zh-CN');
            }
        }
    }
});
</script>
</body>
</html>
