<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务清单</title>
    <link href="https://fonts.loli.net/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Roboto', '微软雅黑', Arial, sans-serif; background: #f7f8fa; margin: 0; height: 100%; }
        .task-container { max-width: 1100px; margin: 30px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 10px #0001; padding: 32px; min-height: calc(100vh - 125px); display: flex; flex-direction: column; position: relative; }
        .view-switch { position: absolute; right: 32px; top: 32px; z-index: 20; }
        @media (max-width: 700px) {
            .view-switch { position: static; margin: 0 0 10px 0; display: block; text-align: right; }
        }
        .task-header { display: flex; flex-wrap: wrap; align-items: flex-start; gap: 16px; margin-bottom: 24px; justify-content: flex-start; }
        .task-header input[type="text"] { flex: 1; padding: 8px 12px; border-radius: 4px; border: 1px solid #ccc; }
        .task-header select { padding: 8px 12px; border-radius: 4px; border: 1px solid #ccc; }
        .view-switch button { margin-left: 8px; padding: 6px 16px; border: none; border-radius: 4px; background: #e9ecef; cursor: pointer; font-weight: 500; }
        .view-switch button.active { background: #007bff; color: #fff; }
        .task-list { }
        .task-item { background: #f1f3f7; margin-bottom: 8px; padding: 14px 18px; border-radius: 5px; display: flex; align-items: flex-start; cursor: grab; box-shadow: 0 1px 2px #0001; position: relative; }
        .task-item .task-title { flex: 1; text-decoration: none; color: #333; line-height: 1.4; word-break: break-word; }
        .task-item.completed .task-title { text-decoration: line-through; color: #888; }
        .task-item.completed { background: #f9f9f9; border-left: 3px solid #8bc34a; padding-left: 15px; margin-bottom: 6px; }
        .task-item button { margin-left: 8px; border: none; background: none; cursor: pointer; font-size: 18px; flex-shrink: 0; }
        .task-item button:focus { outline: none; }
        .task-item button.delete-btn:focus, .task-item button.add-subtask-btn:focus { outline: 1.5px solid #007bff; }
        .task-item button.delete-btn { color: #e53935; transition: color 0.15s; }
        .task-item button.delete-btn:hover { color: #b71c1c; background: #ffeaea; }
        .task-item button.add-subtask-btn { color: #4caf50; transition: color 0.15s; }
        .task-item button.add-subtask-btn:hover { color: #2e7d32; background: #e8f5e9; }
        .task-item button.toggle-btn { color: #666; transition: color 0.15s; font-size: 12px; position: absolute; left: -18px; top: 50%; transform: translateY(-50%); width: 16px; height: 16px; padding: 0; margin: 0; display: flex; align-items: center; justify-content: center; border-radius: 50%; background-color: #fff; box-shadow: 0 1px 2px rgba(0,0,0,0.1); outline: none; }
        .quadrant .task-item.quadrant-task button.toggle-btn { left: -10px; }
        .task-item button.toggle-btn:hover { color: #007bff; background: #e8f5e9; }
        .subtask { margin-left: 30px; position: relative; }
        .subtask::before { content: ''; position: absolute; left: -20px; top: 50%; width: 16px; height: 1px; background: #b3c0d1; }
        .subtask::after { content: ''; position: absolute; left: -20px; top: 0; bottom: 50%; width: 1px; background: #b3c0d1; }
        .subtasks-container { transition: max-height 0.3s ease-out, opacity 0.3s ease-out; overflow: hidden; }
        .subtasks-container.collapsed { max-height: 0; opacity: 0; }
        .quadrant-badge { display: inline-block; width: 20px; height: 20px; border-radius: 50%; color: white; text-align: center; line-height: 20px; font-size: 12px; margin-left: 8px; flex-shrink: 0; }
        .quadrant-badge.q1 { background-color: #e53935; } /* 重要且紧急 - 红色 */
        .quadrant-badge.q2 { background-color: #fb8c00; } /* 重要不紧急 - 橙色 */
        .quadrant-badge.q3 { background-color: #43a047; } /* 不重要但紧急 - 绿色 */
        .quadrant-badge.q4 { background-color: #1e88e5; } /* 不重要不紧急 - 蓝色 */

        /* 错误提示和加载指示器 */
        .error-message { position: fixed; top: 20px; right: 20px; background: #ffebee; color: #d32f2f; padding: 12px 16px; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); display: flex; align-items: center; z-index: 1000; max-width: 80%; }
        .error-message span { flex: 1; margin-right: 12px; }
        .error-message button { background: none; border: none; color: #d32f2f; font-size: 18px; cursor: pointer; padding: 0; }
        .loading-indicator { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,255,255,0.9); padding: 20px; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); display: flex; flex-direction: column; align-items: center; z-index: 1000; }
        .spinner { width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 12px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .quadrant .task-item button.delete-btn { color: #e53935; }
        .quadrant .task-item button.delete-btn:hover { color: #b71c1c; background: #ffeaea; }
        .quadrant .subtask { margin-left: 20px; }
        #doneSection { background: #f7f7fa; border-radius: 6px; padding: 8px 12px; }
        #doneHeader:hover { color: #007bff; }
        #doneTasks { transition: max-height 0.2s; }
        .quadrant-view { display: grid; grid-template-columns: 1fr 1fr; grid-template-rows: 1fr 1fr; gap: 20px; margin-top: 16px; flex: 1; height: 100%; min-height: 0; }
        .quadrant { background: #f8fafc; border: 1.5px dashed #b3c0d1; border-radius: 8px; min-height: 0; padding: 28px 12px 12px 12px; position: relative; overflow: auto; }
        .quadrant.q1 { border-color: #e53935; background-color: rgba(229, 57, 53, 0.05); }
        .quadrant.q2 { border-color: #fb8c00; background-color: rgba(251, 140, 0, 0.05); }
        .quadrant.q3 { border-color: #43a047; background-color: rgba(67, 160, 71, 0.05); }
        .quadrant.q4 { border-color: #1e88e5; background-color: rgba(30, 136, 229, 0.05); }
        .quadrant-title { position: absolute; top: 8px; left: 16px; font-size: 14px; color: #666; font-weight: 500; z-index: 2; padding: 0 8px; }
        .quadrant.q1 .quadrant-title { color: #e53935; background-color: rgba(229, 57, 53, 0.05); }
        .quadrant.q2 .quadrant-title { color: #fb8c00; background-color: rgba(251, 140, 0, 0.05); }
        .quadrant.q3 .quadrant-title { color: #43a047; background-color: rgba(67, 160, 71, 0.05); }
        .quadrant.q4 .quadrant-title { color: #1e88e5; background-color: rgba(30, 136, 229, 0.05); }
        .quadrant .task-item.quadrant-task { background: rgba(255, 255, 255, 0.7); margin: 8px 0; cursor: grab; box-shadow: 0 1px 3px rgba(0,0,0,0.08); padding: 10px 12px 10px 15px; border-radius: 4px; display: flex; align-items: flex-start; position: relative; }
        .quadrant.q1 .task-item.quadrant-task { border-left: 3px solid #e53935; }
        .quadrant.q2 .task-item.quadrant-task { border-left: 3px solid #fb8c00; }
        .quadrant.q3 .task-item.quadrant-task { border-left: 3px solid #43a047; }
        .quadrant.q4 .task-item.quadrant-task { border-left: 3px solid #1e88e5; }
        .quadrant.q1 .task-item.quadrant-task button.toggle-btn { color: #e53935; }
        .quadrant.q2 .task-item.quadrant-task button.toggle-btn { color: #fb8c00; }
        .quadrant.q3 .task-item.quadrant-task button.toggle-btn { color: #43a047; }
        .quadrant.q4 .task-item.quadrant-task button.toggle-btn { color: #1e88e5; }
        .quadrant .task-item.completed .task-title { text-decoration: line-through; color: #888; }
        .quadrant .task-item.completed { background: #f9f9f9; border-left: 3px solid #8bc34a !important; padding-left: 15px; margin-bottom: 6px; }
        .quadrant .task-item button { margin-left: 8px; border: none; background: none; cursor: pointer; font-size: 18px; }
        .quadrant .task-item button:focus { outline: none; }
        .quadrant .task-item button.delete-btn:focus, .quadrant .task-item button.add-subtask-btn:focus { outline: 1.5px solid #007bff; }
        .quadrant .task-item.dragging { opacity: 0.5; }
        .main-title { font-size: 2.1rem; font-weight: 600; letter-spacing: 1px; margin: 0 0 22px 0; color: #222; text-align: center; }
        @media (max-width: 900px) { .task-container { padding: 10px; } }
        @media (max-width: 700px) { .quadrant-view { grid-template-columns: 1fr; grid-template-rows: repeat(4, 1fr); } }
        .modal-overlay {
            position: fixed;
            left: 0; top: 0; right: 0; bottom: 0;
            background: rgba(0,0,0,0.25);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .add-category-modal {
            background: #fff; border-radius: 10px; box-shadow: 0 6px 32px #0002;
            padding: 28px 24px 18px 24px; min-width: 240px; max-width: 90vw;
        }
        .modal-header { font-size: 1.18rem; font-weight: 600; margin-bottom: 16px; }
        .modal-body { margin-bottom: 18px; }
        .modal-footer { display: flex; justify-content: flex-end; gap: 12px; }
        .modal-btn { min-width: 70px; padding: 8px 16px; border-radius: 4px; border: none; font-size: 1rem; cursor: pointer; }
        .modal-btn.cancel { background: #e9ecef; color: #333; }
        .modal-btn.confirm { background: #e53935; color: #fff; }
        /* 美化 checkbox */
        .task-item input[type="checkbox"], .quadrant-task input[type="checkbox"] {
            appearance: none;
            -webkit-appearance: none;
            width: 20px; height: 20px;
            border: 2px solid #b3c0d1;
            border-radius: 50%;
            background: #fff;
            outline: none;
            cursor: pointer;
            transition: border-color 0.2s, box-shadow 0.2s;
            position: relative;
            margin-right: 14px;
            box-shadow: 0 1px 2px #0001;
            display: inline-block;
            flex-shrink: 0;
            margin-top: 2px;
        }
        .task-item input[type="checkbox"]:hover, .quadrant-task input[type="checkbox"]:hover {
            border-color: #007bff;
            box-shadow: 0 0 0 3px #e3f0fb;
        }
        .task-item input[type="checkbox"]:checked, .quadrant-task input[type="checkbox"]:checked {
            border-color: #8bc34a;
            background: linear-gradient(135deg, #9ccc65 60%, #8bc34a 100%);
        }
        .task-item input[type="checkbox"]:checked::after, .quadrant-task input[type="checkbox"]:checked::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            width: 7px;
            height: 12px;
            border: solid #fff;
            border-width: 0 2.5px 2.5px 0;
            border-radius: 1px;
            transform: translate(-50%, -50%) rotate(45deg);
        }
        .task-item input[type="checkbox"]:focus, .quadrant-task input[type="checkbox"]:focus {
            box-shadow: 0 0 0 2px #cfe2ff;
        }
        .edit-input {
            font-size: 1rem;
            font-family: inherit;
            background: #f8fbff;
            color: #222;
            box-sizing: border-box;
            margin-left: 0;
        }
    </style>
</head>
<body>
<div id="app">
    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
        <span>{{ error }}</span>
        <button @click="error = null">×</button>
    </div>

    <!-- 加载中提示 -->
    <div v-if="loading" class="loading-indicator">
        <div class="spinner"></div>
        <span>加载中...</span>
    </div>

    <div class="task-container">
        <h1 class="main-title">任务清单</h1>
        <div class="view-switch">
            <button :class="{active: viewMode==='list'}" @click="viewMode='list'">列表视图</button>
            <button :class="{active: viewMode==='quadrant'}" @click="viewMode='quadrant'">四象限视图</button>
        </div>
        <div class="task-header" v-show="viewMode==='list'">
            <input type="text" v-model="searchKeyword" placeholder="搜索任务...">
            <select v-model="filterYear">
                <option value="">全部年份</option>
                <option v-for="y in years" :key="y" :value="y">{{ y }}年</option>
            </select>
            <select v-model="filterMonth">
                <option value="">全部月份</option>
                <option v-for="m in 12" :key="m" :value="m">{{ m }}月</option>
            </select>
        </div>
        <div id="listView" class="task-list" v-show="viewMode==='list'">
            <div id="todoSection">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
                    <div style="font-weight:bold;color:#333;">待办任务 <span style="margin-left:8px;font-weight:normal;color:#666;">({{ filteredTodoTasks.length }})</span></div>
                    <div id="addTaskBar" style="display:flex;gap:8px;min-width:220px;max-width:420px;width:100%;justify-content:flex-end;">
                        <input type="text" v-model="newTaskTitle" placeholder="输入新任务..." style="flex:1;min-width:100px;max-width:220px;padding:8px 10px;border-radius:4px;border:1px solid #bbb;" @keydown.enter="addTask()">
                        <button @click="addTask()" style="padding:8px 18px;border-radius:4px;border:none;background:#007bff;color:#fff;font-weight:500;cursor:pointer;">添加</button>
                    </div>
                </div>
                <div v-if="filteredTodoTasks.length > 0">
                    <template v-for="(task,idx) in filteredTodoTasks">
                        <!-- 父任务 -->
                        <div class="task-item" :key="task.id" draggable="true"
                            :class="{completed: task.completed}" @dragstart="dragStart(idx, 'todo')" @dragover.prevent @drop="dragDrop(idx, 'todo')">
                            <button
                                v-if="task.subTasks && task.subTasks.length > 0"
                                class="toggle-btn"
                                @click.stop="toggleSubTasks(task.id)"
                                :title="isTaskCollapsed(task.id) ? '展开子任务' : '折叠子任务'"
                            >{{ isTaskCollapsed(task.id) ? '▶' : '▼' }}</button>
                            <input type="checkbox" class="complete-checkbox" :checked="task.completed" @change="markComplete(task, $event)">
                            <span class="task-title" v-if="editTaskId!==task.id" @dblclick="startEdit(task)"><span v-if="!task.completed">{{idx+1}}. </span>{{ task.title }}</span>
                            <input v-else ref="editInput" class="edit-input" v-model="editValue" @keydown.enter="saveEdit" @keydown.esc="cancelEdit" @blur="saveEdit" style="flex:1;min-width:40px;padding:6px 8px;border-radius:4px;border:1.5px solid #007bff;outline:none;">
                            <button class="add-subtask-btn" title="添加子任务" @click="addSubTask(task)" v-if="!task.completed">+</button>
                            <span v-if="task.quadrant" class="quadrant-badge" :class="'q'+task.quadrant" :title="quadrantTitles[task.quadrant-1]">{{ task.quadrant }}</span>
                            <button class="delete-btn" title="删除" @click="deleteTask(task, 'todo')">×</button>
                        </div>

                        <!-- 子任务容器 -->
                        <div class="subtasks-container" :class="{collapsed: isTaskCollapsed(task.id)}">
                            <!-- 子任务 -->
                            <template v-for="(subTask, subIdx) in getSubTasks(task.id)">
                                <div class="task-item subtask" :key="'sub-'+subTask.id" draggable="true"
                                    :class="{completed: subTask.completed}">
                                    <input type="checkbox" class="complete-checkbox" :checked="subTask.completed" @change="markComplete(subTask, $event)">
                                    <span class="task-title" v-if="editTaskId!==subTask.id" @dblclick="startEdit(subTask)">{{ subTask.title }}</span>
                                    <input v-else ref="editInput" class="edit-input" v-model="editValue" @keydown.enter="saveEdit" @keydown.esc="cancelEdit" @blur="saveEdit" style="flex:1;min-width:40px;padding:6px 8px;border-radius:4px;border:1.5px solid #007bff;outline:none;">
                                    <button class="delete-btn" title="删除" @click="deleteTask(subTask, 'todo')">×</button>
                                </div>
                            </template>
                        </div>

                        <!-- 添加子任务的输入框 -->
                        <div v-if="showAddSubTaskInput && subTaskParentId === task.id" class="task-item subtask" style="background:#e8f5e9;" :class="{collapsed: isTaskCollapsed(task.id)}">
                            <input type="text" :ref="'subTaskInput_' + task.id" v-model="newSubTaskTitle" placeholder="输入子任务..."
                                   style="flex:1;min-width:40px;padding:6px 8px;border-radius:4px;border:1px solid #4caf50;outline:none;"
                                   @keydown.enter="saveSubTask" @keydown.esc="cancelAddSubTask">
                            <button @click="saveSubTask" style="color:#4caf50;">✓</button>
                            <button @click="cancelAddSubTask" style="color:#e53935;">×</button>
                        </div>
                    </template>
                </div>
                <div v-else class="empty-tip" style="text-align:center; color:#888; padding:30px 0;border:1px dashed #ddd;border-radius:4px;margin-top:10px;">暂无待办任务</div>
            </div>
            <div id="doneSection" style="margin-top:20px;">
                <div style="display:flex;align-items:center;font-weight:bold;cursor:pointer;user-select:none;border-bottom:1px solid #e0e0e0;padding:8px 0;color:#689f38;" @click="doneCollapsed=!doneCollapsed">
                    <span style="font-size:16px;margin-right:8px;">{{ doneCollapsed ? '▶' : '▼' }}</span>已完成任务 <span style="margin-left:8px;font-weight:normal;color:#8bc34a;">({{ filteredDoneTasks.length }})</span>
                </div>
                <div id="doneTasks" v-show="!doneCollapsed" style="padding:4px 0 0;margin-top:4px;">
                    <template v-for="task in filteredDoneTasks">
                        <!-- 已完成的父任务 -->
                        <div class="task-item completed" :key="task.id">
                            <button
                                v-if="task.subTasks && task.subTasks.length > 0"
                                class="toggle-btn"
                                @click.stop="toggleSubTasks(task.id)"
                                :title="isTaskCollapsed(task.id) ? '展开子任务' : '折叠子任务'"
                            >{{ isTaskCollapsed(task.id) ? '▶' : '▼' }}</button>
                            <input type="checkbox" class="complete-checkbox" :checked="task.completed" @change="markComplete(task, $event)">
                            <span class="task-title" v-if="editTaskId!==task.id" @dblclick="startEdit(task)">{{ task.title }}</span>
                            <input v-else ref="editInput" class="edit-input" v-model="editValue" @keydown.enter="saveEdit" @keydown.esc="cancelEdit" @blur="saveEdit" style="flex:1;min-width:40px;padding:6px 8px;border-radius:4px;border:1.5px solid #007bff;outline:none;">
                            <button class="delete-btn" title="删除" @click="deleteTask(task, 'done')">×</button>
                        </div>

                        <!-- 子任务容器 -->
                        <div class="subtasks-container" :class="{collapsed: isTaskCollapsed(task.id)}">
                            <!-- 子任务 -->
                            <template v-for="subTask in getSubTasks(task.id)">
                                <div class="task-item subtask completed" :key="'sub-'+subTask.id">
                                    <input type="checkbox" class="complete-checkbox" :checked="subTask.completed" @change="markComplete(subTask, $event)">
                                    <span class="task-title" v-if="editTaskId!==subTask.id" @dblclick="startEdit(subTask)">{{ subTask.title }}</span>
                                    <input v-else ref="editInput" class="edit-input" v-model="editValue" @keydown.enter="saveEdit" @keydown.esc="cancelEdit" @blur="saveEdit" style="flex:1;min-width:40px;padding:6px 8px;border-radius:4px;border:1.5px solid #007bff;outline:none;">
                                    <button class="delete-btn" title="删除" @click="deleteTask(subTask, 'done')">×</button>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
                <div v-else class="empty-tip" style="text-align:center; color:#888; padding:12px 0;margin-top:4px;font-size:14px;">暂无已完成任务</div>
            </div>
        </div>
        <div id="quadrantView" class="quadrant-view" v-show="viewMode==='quadrant'">
            <div class="quadrant" :class="'q'+q" v-for="q in 4" :key="q" @dragover.prevent @drop="quadrantDrop(q)">
                <span class="quadrant-title">{{ quadrantTitles[q-1] }}</span>
                <template v-for="(task,idx) in quadrantTasks[q]">
                    <!-- 父任务 -->
                    <div class="task-item quadrant-task" :key="task.id" :class="{completed: task.completed}"
                        draggable="true" @dragstart="dragStart(idx, q)" @dragover.prevent @drop="dragDrop(idx, q)">
                        <button
                            v-if="task.subTasks && task.subTasks.length > 0"
                            class="toggle-btn"
                            @click.stop="toggleSubTasks(task.id)"
                            :title="isTaskCollapsed(task.id) ? '展开子任务' : '折叠子任务'"
                        >{{ isTaskCollapsed(task.id) ? '▶' : '▼' }}</button>
                        <input type="checkbox" class="quadrant-complete-checkbox" :checked="task.completed" @change="markComplete(task, $event)">
                        <span class="task-title" v-if="editTaskId!==task.id" @dblclick="startEdit(task)"><span v-if="!task.completed">{{idx+1}}. </span>{{ task.title }}</span>
                        <input v-else ref="editInput" class="edit-input" v-model="editValue" @keydown.enter="saveEdit" @keydown.esc="cancelEdit" @blur="saveEdit" style="flex:1;min-width:40px;padding:6px 8px;border-radius:4px;border:1.5px solid #007bff;outline:none;">
                        <button class="add-subtask-btn" title="添加子任务" @click="addSubTask(task)" v-if="!task.completed">+</button>
                        <button class="delete-btn" title="删除" @click="deleteTask(task, q)">×</button>
                    </div>

                    <!-- 子任务容器 -->
                    <div class="subtasks-container" :class="{collapsed: isTaskCollapsed(task.id)}" :key="'container-'+task.id" v-if="task.subTasks && task.subTasks.length > 0">
                        <!-- 子任务 -->
                        <template v-for="(subTask, subIdx) in getSubTasks(task.id)">
                            <div class="task-item subtask" :key="'sub-'+subTask.id" draggable="true"
                                :class="{completed: subTask.completed}">
                                <input type="checkbox" class="complete-checkbox" :checked="subTask.completed" @change="markComplete(subTask, $event)">
                                <span class="task-title" v-if="editTaskId!==subTask.id" @dblclick="startEdit(subTask)">{{ subTask.title }}</span>
                                <input v-else ref="editInput" class="edit-input" v-model="editValue" @keydown.enter="saveEdit" @keydown.esc="cancelEdit" @blur="saveEdit" style="flex:1;min-width:40px;padding:6px 8px;border-radius:4px;border:1.5px solid #007bff;outline:none;">
                                <button class="delete-btn" title="删除" @click="deleteTask(subTask, q)">×</button>
                            </div>
                        </template>
                    </div>

                    <!-- 添加子任务的输入框 -->
                    <div :key="'input-'+task.id" v-if="showAddSubTaskInput && subTaskParentId === task.id" class="task-item subtask" style="background:#e8f5e9;" :class="{collapsed: isTaskCollapsed(task.id)}">
                        <input type="text" :ref="'subTaskInput_' + task.id" v-model="newSubTaskTitle" placeholder="输入子任务..."
                               style="flex:1;min-width:40px;padding:6px 8px;border-radius:4px;border:1px solid #4caf50;outline:none;"
                               @keydown.enter="saveSubTask" @keydown.esc="cancelAddSubTask">
                        <button @click="saveSubTask" style="color:#4caf50;">✓</button>
                        <button @click="cancelAddSubTask" style="color:#e53935;">×</button>
                    </div>
                </template>
            </div>
        </div>
    </div>
    <!-- 确认删除弹窗 -->
    <div v-if="showConfirm" class="modal-overlay" style="z-index:9999;">
        <div class="add-category-modal" style="max-width:320px;">
            <div class="modal-header">确认删除</div>
            <div class="modal-body">确定要删除该任务吗？</div>
            <div class="modal-footer">
                <button class="modal-btn cancel" @click="showConfirm=false">取消</button>
                <button class="modal-btn confirm" style="background:#e53935;color:#fff;" @click="confirmDelete">删除</button>
            </div>
        </div>
    </div>
</div>
<script src="js/libs/vue.min.js"></script>
<script src="js/task-api.js"></script>
<script>
new Vue({
    el: '#app',
    data: {
        viewMode: 'list',
        searchKeyword: '',
        filterYear: '',
        filterMonth: '',
        years: (function(){
            const now = new Date().getFullYear();
            let arr = [];
            for(let y=now; y>=now-5; y--) arr.push(y);
            return arr;
        })(),
        newTaskTitle: '',
        newSubTaskTitle: '',
        showAddSubTaskInput: false,
        subTaskParentId: null,
        collapsedTasks: {}, // 存储每个父任务的折叠状态
        loading: false,
        error: null,
        tasks: [],
        doneCollapsed: false,
        dragTask: null,
        dragFrom: null,
        quadrantTitles: ['重要且紧急','重要不紧急','不重要但紧急','不重要不紧急'],
        showConfirm: false,
        pendingDelete: null,
        pendingDeleteType: null,
        editTaskId: null,
        editValue: '',
    },
    created() {
        this.loadTasks();
    },
    computed: {
        filteredTodoTasks() {
            // 只获取顶级任务（没有父任务的任务）
            // 包括没有象限的任务和有象限的任务
            const tasks = this.tasks.filter(t => !t.completed && !t.parentId && this.filterTask(t));

            // 按照排序号排序
            return tasks.sort((a, b) => {
                // 如果没有排序号，则使用ID排序
                const orderA = a.orderNo || 0;
                const orderB = b.orderNo || 0;
                return orderA - orderB;
            });
        },
        filteredDoneTasks() {
            // 只获取顶级任务（没有父任务的任务）
            const tasks = this.tasks.filter(t => t.completed && !t.parentId && this.filterTask(t));

            // 按照排序号排序
            return tasks.sort((a, b) => {
                // 如果没有排序号，则使用ID排序
                const orderA = a.orderNo || 0;
                const orderB = b.orderNo || 0;
                return orderA - orderB;
            });
        },


        quadrantTasks() {
            // {1:[],2:[],3:[],4:[]}
            const q = {1:[],2:[],3:[],4:[]};
            this.tasks.forEach(t => {
                if(t.quadrant && !t.completed && !t.parentId && this.filterTask(t)) q[t.quadrant].push(t);
            });

            // 按照排序号排序
            for (let i = 1; i <= 4; i++) {
                q[i].sort((a, b) => {
                    // 如果没有排序号，则使用ID排序
                    const orderA = a.orderNo || 0;
                    const orderB = b.orderNo || 0;
                    return orderA - orderB;
                });
            }

            return q;
        }
    },
    methods: {
        filterTask(t) {
            let ok = true;
            if(this.searchKeyword && t.title.indexOf(this.searchKeyword)===-1) ok = false;
            if(this.filterYear && t.year != this.filterYear) ok = false;
            if(this.filterMonth && t.month != this.filterMonth) ok = false;
            return ok;
        },
        // 获取指定父任务的子任务
        getSubTasks(parentId) {
            // 确保返回所有子任务，无论是否完成
            return this.tasks.filter(t => t.parentId === parentId && this.filterTask(t));
        },
        // 检查任务是否有未完成的子任务
        hasUncompletedSubTasks(task) {
            if (!task.subTasks || task.subTasks.length === 0) return false;
            return task.subTasks.some(subTaskId => {
                const subTask = this.tasks.find(t => t.id === subTaskId);
                return subTask && !subTask.completed;
            });
        },
        // 检查任务是否所有子任务都已完成
        allSubTasksCompleted(task) {
            if (!task.subTasks || task.subTasks.length === 0) return true;
            return task.subTasks.every(subTaskId => {
                const subTask = this.tasks.find(t => t.id === subTaskId);
                return subTask && subTask.completed;
            });
        },
        async loadTasks() {
            try {
                this.loading = true;
                this.error = null;

                // 构建查询参数
                const params = {};
                if (this.filterYear) params.year = this.filterYear;
                if (this.filterMonth) params.month = this.filterMonth;
                if (this.searchKeyword) params.keyword = this.searchKeyword;

                // 获取任务列表
                const tasks = await TaskApi.getTasks(params);
                this.tasks = tasks;

                // 加载每个父任务的子任务
                for (const task of tasks) {
                    if (task.subTasks && task.subTasks.length > 0) {
                        try {
                            const subTasks = await TaskApi.getSubTasks(task.id);
                            // 将子任务添加到任务列表中
                            this.tasks = [...this.tasks, ...subTasks];
                        } catch (subError) {
                            console.error(`加载任务 ${task.id} 的子任务失败:`, subError);
                        }
                    }
                }
            } catch (error) {
                this.error = `加载任务失败: ${error.message}`;
                console.error('加载任务失败:', error);
            } finally {
                this.loading = false;
            }
        },

        async addTask(parentId = null) {
            const val = this.newTaskTitle.trim();
            if(!val) return;

            try {
                const newTask = {
                    title: val,
                    year: new Date().getFullYear(),
                    month: new Date().getMonth() + 1,
                    parentId: parentId,
                    quadrant: parentId ? null : 4 // 如果不是子任务，默认放到第4象限
                };

                // 调用API创建任务
                const createdTask = await TaskApi.createTask(newTask);

                // 更新本地数据
                if (parentId) {
                    // 如果是子任务，更新父任务的子任务列表
                    const parentTask = this.tasks.find(t => t.id === parentId);
                    if (parentTask) {
                        if (!parentTask.subTasks) {
                            parentTask.subTasks = [];
                        }
                        parentTask.subTasks.push(createdTask.id);
                    }
                }

                // 添加到任务列表
                this.tasks.unshift(createdTask);
                this.newTaskTitle = '';
            } catch (error) {
                this.error = `创建任务失败: ${error.message}`;
                console.error('创建任务失败:', error);
            }
        },

        // 添加子任务
        addSubTask(parentTask) {
            // 显示添加子任务的输入框
            this.showAddSubTaskInput = true;
            this.subTaskParentId = parentTask.id;

            // 确保子任务列表展开
            if (this.isTaskCollapsed(parentTask.id)) {
                this.toggleSubTasks(parentTask.id);
            }

            this.$nextTick(() => {
                const refName = 'subTaskInput_' + parentTask.id;
                if (this.$refs[refName]) {
                    // 可能是数组或单个元素
                    if (Array.isArray(this.$refs[refName])) {
                        this.$refs[refName][0].focus();
                    } else {
                        this.$refs[refName].focus();
                    }
                }
            });
        },

        // 保存子任务
        async saveSubTask() {
            const val = this.newSubTaskTitle.trim();
            if (!val) return;

            try {
                const newTask = {
                    title: val,
                    parentId: this.subTaskParentId,
                    year: new Date().getFullYear(),
                    month: new Date().getMonth()+1
                };

                // 调用API创建子任务
                const createdTask = await TaskApi.createTask(newTask);

                // 将子任务ID添加到父任务的subTasks数组中
                const parentTask = this.tasks.find(t => t.id === this.subTaskParentId);
                if (parentTask) {
                    if (!parentTask.subTasks) {
                        parentTask.subTasks = [];
                    }
                    parentTask.subTasks.push(createdTask.id);

                    // 确保子任务列表展开
                    if (this.isTaskCollapsed(parentTask.id)) {
                        this.toggleSubTasks(parentTask.id);
                    }
                }

                // 添加到任务列表
                this.tasks.push(createdTask);
                this.newSubTaskTitle = '';
                this.showAddSubTaskInput = false;
                this.subTaskParentId = null;
            } catch (error) {
                this.error = `创建子任务失败: ${error.message}`;
                console.error('创建子任务失败:', error);
            }
        },

        // 取消添加子任务
        cancelAddSubTask() {
            this.newSubTaskTitle = '';
            this.showAddSubTaskInput = false;
            this.subTaskParentId = null;
        },

        // 切换子任务的折叠/展开状态
        toggleSubTasks(taskId) {
            this.$set(this.collapsedTasks, taskId, !this.isTaskCollapsed(taskId));
        },

        // 检查任务是否已折叠
        isTaskCollapsed(taskId) {
            return this.collapsedTasks[taskId] === true;
        },
        async markComplete(task, e) {
            const isCompleted = e.target.checked;

            try {
                // 调用API更新任务完成状态
                const updatedTask = await TaskApi.markTaskCompletion(task.id, isCompleted);

                // 更新本地任务状态
                task.completed = isCompleted;

                // 如果任务被标记为完成
                if (isCompleted) {
                    // 如果有子任务，将所有子任务也标记为完成
                    if (task.subTasks && task.subTasks.length > 0) {
                        for (const subTaskId of task.subTasks) {
                            const subTask = this.tasks.find(t => t.id === subTaskId);
                            if (subTask && !subTask.completed) {
                                subTask.completed = true;
                            }
                        }
                    }
                } else {
                    // 如果任务被标记为未完成，且该任务有父任务，则父任务也标记为未完成
                    if (task.parentId) {
                        const parentTask = this.tasks.find(t => t.id === task.parentId);
                        if (parentTask && parentTask.completed) {
                            parentTask.completed = false;
                        }
                    }
                }

                // 如果在四象限视图，勾选后自动移出该象限
                if(task.quadrant && task.completed) {
                    // nothing else needed, computed会自动刷新
                }
            } catch (error) {
                // 恢复原始状态
                e.target.checked = !isCompleted;
                task.completed = !isCompleted;

                this.error = `更新任务状态失败: ${error.message}`;
                console.error('更新任务状态失败:', error);
            }
        },
        deleteTask(task, type) {
            this.showConfirm = true;
            this.pendingDelete = task;
            this.pendingDeleteType = type;
        },
        async confirmDelete() {
            const task = this.pendingDelete;
            const type = this.pendingDeleteType;

            try {
                // 调用API删除任务
                await TaskApi.deleteTask(task.id);

                // 更新本地数据
                // 如果是父任务，同时删除所有子任务
                if (task.subTasks && task.subTasks.length > 0) {
                    // 复制一份子任务ID数组，因为我们会在循环中修改原数组
                    const subTaskIds = [...task.subTasks];
                    subTaskIds.forEach(subTaskId => {
                        const subTaskIdx = this.tasks.findIndex(t => t.id === subTaskId);
                        if (subTaskIdx > -1) {
                            this.tasks.splice(subTaskIdx, 1);
                        }
                    });
                }

                // 如果是子任务，从父任务的subTasks数组中移除
                if (task.parentId) {
                    const parentTask = this.tasks.find(t => t.id === task.parentId);
                    if (parentTask && parentTask.subTasks) {
                        const subTaskIdx = parentTask.subTasks.indexOf(task.id);
                        if (subTaskIdx > -1) {
                            parentTask.subTasks.splice(subTaskIdx, 1);
                        }
                    }
                }

                // 删除任务本身
                const idx = this.tasks.findIndex(t => t.id === task.id);
                if(idx > -1) this.tasks.splice(idx, 1);
            } catch (error) {
                this.error = `删除任务失败: ${error.message}`;
                console.error('删除任务失败:', error);
            } finally {
                this.showConfirm = false;
                this.pendingDelete = null;
                this.pendingDeleteType = null;
            }
        },
        dragStart(idx, type) {
            if(type==='todo') {
                this.dragTask = this.filteredTodoTasks[idx];
                this.dragFrom = this.tasks.indexOf(this.dragTask);
            } else if(typeof type==='number') {
                // quadrant number
                this.dragTask = this.quadrantTasks[type][idx];
                this.dragFrom = this.tasks.indexOf(this.dragTask);
            }
        },
        async dragDrop(idx, type) {
            if(!this.dragTask) return;

            try {
                if(type==='todo') {
                    const list = this.filteredTodoTasks;
                    const toTask = list[idx];
                    const toIdx = this.tasks.indexOf(toTask);

                    // 如果任务已完成，标记为未完成
                    if (this.dragTask.completed) {
                        // 调用API更新任务状态
                        await TaskApi.markTaskCompletion(this.dragTask.id, false);
                        this.dragTask.completed = false;
                    }

                    // 如果是同一个列表中的拖拽，调用交换顺序的API
                    if (this.dragTask.id !== toTask.id) {
                        await TaskApi.swapTaskOrder(this.dragTask.id, toTask.id);
                    }

                    this.tasks.splice(this.dragFrom,1);
                    this.tasks.splice(toIdx,0,this.dragTask);
                } else if(typeof type==='number') {
                    const list = this.quadrantTasks[type];
                    const toTask = list[idx];
                    const toIdx = this.tasks.indexOf(toTask);

                    // 如果任务象限改变，更新任务
                    if (this.dragTask.quadrant !== type || this.dragTask.completed) {
                        // 调用API更新任务
                        await TaskApi.updateTask({
                            id: this.dragTask.id,
                            title: this.dragTask.title,
                            year: this.dragTask.year,
                            month: this.dragTask.month,
                            quadrant: type
                        });

                        // 如果任务已完成，标记为未完成
                        if (this.dragTask.completed) {
                            await TaskApi.markTaskCompletion(this.dragTask.id, false);
                            this.dragTask.completed = false;
                        }

                        this.dragTask.quadrant = type;
                    } else if (this.dragTask.id !== toTask.id && this.dragTask.quadrant === type) {
                        // 如果是同一个象限中的拖拽，调用交换顺序的API
                        await TaskApi.swapTaskOrder(this.dragTask.id, toTask.id);
                    }

                    this.tasks.splice(this.dragFrom,1);
                    this.tasks.splice(toIdx,0,this.dragTask);
                }
            } catch (error) {
                this.error = `移动任务失败: ${error.message}`;
                console.error('移动任务失败:', error);
            } finally {
                this.dragTask = null;
                this.dragFrom = null;
            }
        },
        async quadrantDrop(q) {
            if(this.dragTask && this.dragTask.quadrant!==q) {
                try {
                    // 调用API更新任务
                    await TaskApi.updateTask({
                        id: this.dragTask.id,
                        title: this.dragTask.title,
                        year: this.dragTask.year,
                        month: this.dragTask.month,
                        quadrant: q
                    });

                    // 如果任务已完成，标记为未完成
                    if (this.dragTask.completed) {
                        await TaskApi.markTaskCompletion(this.dragTask.id, false);
                        this.dragTask.completed = false;
                    }

                    this.dragTask.quadrant = q;
                } catch (error) {
                    this.error = `移动任务失败: ${error.message}`;
                    console.error('移动任务失败:', error);
                }
                this.dragTask = null;
                this.dragFrom = null;
            }
        },
        startEdit(task) {
            if (task.completed) return;
            this.editTaskId = task.id;
            this.editValue = task.title;
            this.$nextTick(()=>{
                if(this.$refs.editInput) {
                    // 可能是数组
                    if(Array.isArray(this.$refs.editInput)) {
                        const input = this.$refs.editInput.find(input=>input && input.value!==undefined);
                        input && input.focus();
                    } else {
                        this.$refs.editInput.focus();
                    }
                }
            });
        },
        async saveEdit() {
            if(!this.editValue.trim()) return this.cancelEdit();

            try {
                const task = this.tasks.find(t => t.id === this.editTaskId);
                if(task) {
                    // 准备更新数据
                    const updateData = {
                        id: task.id,
                        title: this.editValue.trim(),
                        year: task.year,
                        month: task.month,
                        quadrant: task.quadrant
                    };

                    // 调用API更新任务
                    const updatedTask = await TaskApi.updateTask(updateData);

                    // 更新本地数据
                    task.title = this.editValue.trim();
                }
            } catch (error) {
                this.error = `更新任务失败: ${error.message}`;
                console.error('更新任务失败:', error);
            } finally {
                this.editTaskId = null;
                this.editValue = '';
            }
        },
        cancelEdit() {
            this.editTaskId = null;
            this.editValue = '';
        },
    }
});
</script>
</body>
</html>
