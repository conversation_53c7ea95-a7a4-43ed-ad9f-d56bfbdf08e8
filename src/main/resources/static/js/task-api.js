/**
 * 任务API服务
 */
const TaskApi = {
    /**
     * 获取任务列表
     * @param {Object} params - 查询参数
     * @returns {Promise<Array>} 任务列表
     */
    getTasks: async function(params = {}) {
        const queryParams = new URLSearchParams();
        if (params.completed !== undefined) queryParams.append('completed', params.completed);
        if (params.year) queryParams.append('year', params.year);
        if (params.month) queryParams.append('month', params.month);
        if (params.keyword) queryParams.append('keyword', params.keyword);

        const response = await fetch(`api/tasks?${queryParams.toString()}`);
        if (!response.ok) {
            throw new Error(`获取任务列表失败: ${response.statusText}`);
        }
        return await response.json();
    },

    /**
     * 获取指定象限的任务
     * @param {number} quadrant - 象限
     * @returns {Promise<Array>} 任务列表
     */
    getTasksByQuadrant: async function(quadrant) {
        const response = await fetch(`api/tasks/quadrant/${quadrant}`);
        if (!response.ok) {
            throw new Error(`获取象限任务失败: ${response.statusText}`);
        }
        return await response.json();
    },

    /**
     * 获取子任务
     * @param {number} parentId - 父任务ID
     * @returns {Promise<Array>} 子任务列表
     */
    getSubTasks: async function(parentId) {
        const response = await fetch(`api/tasks/${parentId}/subtasks`);
        if (!response.ok) {
            throw new Error(`获取子任务失败: ${response.statusText}`);
        }
        return await response.json();
    },

    /**
     * 创建任务
     * @param {Object} task - 任务数据
     * @returns {Promise<Object>} 创建后的任务
     */
    createTask: async function(task) {
        const response = await fetch('api/tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(task)
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`创建任务失败: ${errorText}`);
        }
        return await response.json();
    },

    /**
     * 更新任务
     * @param {Object} task - 任务数据
     * @returns {Promise<Object>} 更新后的任务
     */
    updateTask: async function(task) {
        const response = await fetch('api/tasks', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(task)
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`更新任务失败: ${errorText}`);
        }
        return await response.json();
    },

    /**
     * 标记任务完成状态
     * @param {number} id - 任务ID
     * @param {boolean} completed - 是否完成
     * @returns {Promise<Object>} 更新后的任务
     */
    markTaskCompletion: async function(id, completed) {
        const response = await fetch('api/tasks/completion', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                id: id,
                completed: completed
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`标记任务状态失败: ${errorText}`);
        }
        return await response.json();
    },

    /**
     * 删除任务
     * @param {number} id - 任务ID
     * @returns {Promise<void>}
     */
    deleteTask: async function(id) {
        const response = await fetch(`api/tasks/${id}`, {
            method: 'DELETE'
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`删除任务失败: ${errorText}`);
        }
    },

    /**
     * 交换任务顺序
     * @param {number} draggedId - 被拖动的任务ID
     * @param {number} targetId - 目标任务ID
     * @returns {Promise<void>}
     */
    swapTaskOrder: async function(draggedId, targetId) {
        const response = await fetch('api/tasks/order', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                draggedId: draggedId,
                targetId: targetId
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`交换任务顺序失败: ${errorText}`);
        }
    }
};
