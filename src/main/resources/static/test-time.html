<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间格式化测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .time-info { font-size: 12px; color: #888; margin-top: 5px; }
        .time-label { font-weight: 500; margin-right: 4px; }
        .time-value { margin-right: 12px; }
    </style>
</head>
<body>
<div id="app">
    <h1>时间格式化测试</h1>
    
    <div class="test-item">
        <h3>测试时间格式化函数</h3>
        <div>
            <strong>当前时间:</strong> {{ new Date().toLocaleString() }}
        </div>
        <div>
            <strong>1分钟前:</strong> {{ formatTime(new Date(Date.now() - 60000)) }}
        </div>
        <div>
            <strong>1小时前:</strong> {{ formatTime(new Date(Date.now() - 3600000)) }}
        </div>
        <div>
            <strong>1天前:</strong> {{ formatTime(new Date(Date.now() - 86400000)) }}
        </div>
        <div>
            <strong>1周前:</strong> {{ formatTime(new Date(Date.now() - 604800000)) }}
        </div>
    </div>
    
    <div class="test-item" v-for="task in tasks" :key="task.id">
        <div><strong>任务:</strong> {{ task.title }}</div>
        <div class="time-info">
            <span class="time-label">创建:</span><span class="time-value">{{ formatTime(task.createdDate) }}</span>
            <span class="time-label">更新:</span><span class="time-value">{{ formatTime(task.lastModifiedDate) }}</span>
        </div>
    </div>
</div>

<script src="js/libs/vue.min.js"></script>
<script>
new Vue({
    el: '#app',
    data: {
        tasks: []
    },
    async created() {
        try {
            const response = await fetch('/api/tasks');
            this.tasks = await response.json();
        } catch (error) {
            console.error('加载任务失败:', error);
        }
    },
    methods: {
        // 格式化时间为友好显示
        formatTime(dateStr) {
            if (!dateStr) return '';
            
            const date = new Date(dateStr);
            const now = new Date();
            const diffMs = now - date;
            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
            
            if (diffMinutes < 1) {
                return '刚刚';
            } else if (diffMinutes < 60) {
                return `${diffMinutes}分钟前`;
            } else if (diffHours < 24) {
                return `${diffHours}小时前`;
            } else if (diffDays < 7) {
                return `${diffDays}天前`;
            } else {
                // 超过一周显示具体日期
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
        }
    }
});
</script>
</body>
</html>
